@model IEnumerable<OfficersManagement.Models.Officer>

@{
    ViewData["Title"] = "قائمة الضباط والمنتسبين";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>
                        قائمة الضباط والمنتسبين
                    </h3>
                </div>
                <div class="card-body">
                    <!-- شريط البحث والإضافة -->
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <form asp-action="Index" method="get" class="d-flex">
                                <input type="text" name="SearchString" value="@ViewData["CurrentFilter"]" 
                                       class="form-control me-2" placeholder="البحث بالاسم، رقم الموظف، أو الرتبة..." />
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <a asp-action="Index" class="btn btn-outline-secondary ms-2">
                                    <i class="fas fa-refresh"></i> إعادة تعيين
                                </a>
                            </form>
                        </div>
                        <div class="col-md-4 text-end">
                            <a asp-action="Create" class="btn btn-success">
                                <i class="fas fa-plus"></i> إضافة ضابط جديد
                            </a>
                        </div>
                    </div>

                    <!-- رسائل النجاح -->
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <!-- الجدول -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>
                                        <a asp-action="Index" asp-route-sortOrder="@ViewData["NameSortParm"]" 
                                           asp-route-searchString="@ViewData["CurrentFilter"]" class="text-white text-decoration-none">
                                            الاسم الكامل
                                            <i class="fas fa-sort"></i>
                                        </a>
                                    </th>
                                    <th>رقم الموظف</th>
                                    <th>
                                        <a asp-action="Index" asp-route-sortOrder="@ViewData["RankSortParm"]" 
                                           asp-route-searchString="@ViewData["CurrentFilter"]" class="text-white text-decoration-none">
                                            الرتبة
                                            <i class="fas fa-sort"></i>
                                        </a>
                                    </th>
                                    <th>
                                        <a asp-action="Index" asp-route-sortOrder="@ViewData["DateSortParm"]" 
                                           asp-route-searchString="@ViewData["CurrentFilter"]" class="text-white text-decoration-none">
                                            العمر
                                            <i class="fas fa-sort"></i>
                                        </a>
                                    </th>
                                    <th>محافظة السكن</th>
                                    <th>الحالة الاجتماعية</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            <strong>@item.FullName</strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">@item.EmployeeNumber</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">@item.Rank</span>
                                        </td>
                                        <td>@item.Age</td>
                                        <td>@item.ResidenceProvince</td>
                                        <td>
                                            @Html.DisplayFor(modelItem => item.MaritalStatus)
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a asp-action="Delete" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    @if (!Model.Any())
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد بيانات للعرض</h5>
                            <p class="text-muted">لم يتم العثور على أي ضباط أو منتسبين</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة أول ضابط
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
}
