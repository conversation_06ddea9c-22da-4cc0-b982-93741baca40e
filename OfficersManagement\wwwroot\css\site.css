/* تحسينات للغة العربية */
html {
  font-size: 14px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif, 'Arabic UI Text', 'Traditional Arabic';
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

body {
  margin-bottom: 60px;
  direction: rtl;
  text-align: right;
}

/* تحسينات الأزرار */
.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

.btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* تحسينات البطاقات */
.card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.card-header {
  border-radius: 12px 12px 0 0 !important;
  border-bottom: none;
  font-weight: 600;
}

/* تحسينات الجدول */
.table {
  border-radius: 8px;
  overflow: hidden;
}

.table thead th {
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.875rem;
  letter-spacing: 0.5px;
}

.table tbody tr:hover {
  background-color: rgba(0,123,255,0.05);
}

/* تحسينات النماذج */
.form-control, .form-select {
  border-radius: 8px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13,110,253,0.25);
}

.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

/* تحسينات الشارات */
.badge {
  border-radius: 6px;
  font-weight: 500;
  padding: 0.5em 0.75em;
}

/* تحسينات الجمبوترون */
.jumbotron {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
}

/* تحسينات الفوتر */
.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 60px;
  line-height: 60px;
}

/* تحسينات الرسائل */
.alert {
  border-radius: 10px;
  border: none;
  font-weight: 500;
}

.alert-success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
}

.alert-danger {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
}

.alert-warning {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  color: #856404;
}

/* تحسينات الشريط العلوي */
.navbar-brand {
  font-weight: 700;
  font-size: 1.25rem;
}

.nav-link {
  font-weight: 500;
  transition: all 0.3s ease;
}

.nav-link:hover {
  transform: translateY(-1px);
}

/* تحسينات الأيقونات */
.fas, .far {
  margin-left: 0.5rem;
}

/* تحسينات للاستجابة */
@media (max-width: 768px) {
  .container-fluid {
    padding: 0 10px;
  }

  .card {
    margin-bottom: 1rem;
  }

  .btn-group {
    flex-direction: column;
  }

  .btn-group .btn {
    margin-bottom: 0.25rem;
  }
}

/* تحسينات إضافية للنصوص العربية */
.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* تحسينات للطباعة */
@media print {
  .btn, .navbar, .footer {
    display: none !important;
  }

  .card {
    border: 1px solid #000 !important;
    box-shadow: none !important;
  }
}