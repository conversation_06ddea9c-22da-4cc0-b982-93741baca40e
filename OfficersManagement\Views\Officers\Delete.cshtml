@model OfficersManagement.Models.Officer

@{
    ViewData["Title"] = "حذف الضابط";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد حذف الضابط
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هل أنت متأكد من أنك تريد حذف هذا الضابط؟ هذا الإجراء لا يمكن التراجع عنه.
                    </div>

                    <!-- معلومات الضابط المراد حذفه -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-user me-2"></i>
                                معلومات الضابط المراد حذفه
                            </h5>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">رقم الموظف:</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-secondary fs-6">@Html.DisplayFor(model => model.EmployeeNumber)</span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">الرقم الإحصائي:</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-secondary fs-6">@Html.DisplayFor(model => model.StatisticalNumber)</span>
                            </p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label fw-bold">الاسم الكامل:</label>
                            <p class="form-control-plaintext fs-5 fw-bold text-dark">
                                @Html.DisplayFor(model => model.FullName)
                            </p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">الرتبة:</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-info fs-6">@Html.DisplayFor(model => model.Rank)</span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">العمر:</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-warning text-dark fs-6">@Html.DisplayFor(model => model.Age)</span>
                            </p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">محل الولادة:</label>
                            <p class="form-control-plaintext">@Html.DisplayFor(model => model.PlaceOfBirth)</p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">محافظة السكن:</label>
                            <p class="form-control-plaintext">@Html.DisplayFor(model => model.ResidenceProvince)</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">الحالة الاجتماعية:</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-success fs-6">@Html.DisplayFor(model => model.MaritalStatus)</span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">تاريخ الإنشاء:</label>
                            <p class="form-control-plaintext">@Html.DisplayFor(model => model.CreatedAt)</p>
                        </div>
                    </div>

                    <!-- نموذج الحذف -->
                    <form asp-action="Delete" class="mt-4">
                        <input type="hidden" asp-for="Id" />
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>
                                        العودة للقائمة
                                    </a>
                                    <div>
                                        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info me-2">
                                            <i class="fas fa-eye me-2"></i>
                                            عرض التفاصيل
                                        </a>
                                        <button type="submit" class="btn btn-danger" 
                                                onclick="return confirm('هل أنت متأكد من حذف هذا الضابط؟')">
                                            <i class="fas fa-trash me-2"></i>
                                            تأكيد الحذف
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
}
