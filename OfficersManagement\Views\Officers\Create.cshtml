@model OfficersManagement.Models.Officer

@{
    ViewData["Title"] = "إضافة ضابط جديد";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة ضابط جديد
                    </h3>
                </div>
                <div class="card-body">
                    <form asp-action="Create">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>
                        
                        <!-- معلومات الهوية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-id-card me-2"></i>
                                    معلومات الهوية
                                </h5>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label asp-for="EmployeeNumber" class="form-label"></label>
                                <input asp-for="EmployeeNumber" class="form-control" />
                                <span asp-validation-for="EmployeeNumber" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label asp-for="StatisticalNumber" class="form-label"></label>
                                <input asp-for="StatisticalNumber" class="form-control" />
                                <span asp-validation-for="StatisticalNumber" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label asp-for="Rank" class="form-label"></label>
                                <select asp-for="Rank" class="form-select">
                                    <option value="">اختر الرتبة</option>
                                    <option value="فريق أول">فريق أول</option>
                                    <option value="فريق">فريق</option>
                                    <option value="لواء">لواء</option>
                                    <option value="عميد">عميد</option>
                                    <option value="عقيد">عقيد</option>
                                    <option value="مقدم">مقدم</option>
                                    <option value="رائد">رائد</option>
                                    <option value="نقيب">نقيب</option>
                                    <option value="ملازم أول">ملازم أول</option>
                                    <option value="ملازم">ملازم</option>
                                    <option value="رقيب أول">رقيب أول</option>
                                    <option value="رقيب">رقيب</option>
                                    <option value="عريف">عريف</option>
                                    <option value="جندي أول">جندي أول</option>
                                    <option value="جندي">جندي</option>
                                </select>
                                <span asp-validation-for="Rank" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- المعلومات الشخصية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-user me-2"></i>
                                    المعلومات الشخصية
                                </h5>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label asp-for="FirstName" class="form-label"></label>
                                <input asp-for="FirstName" class="form-control" />
                                <span asp-validation-for="FirstName" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label asp-for="SecondName" class="form-label"></label>
                                <input asp-for="SecondName" class="form-control" />
                                <span asp-validation-for="SecondName" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label asp-for="ThirdName" class="form-label"></label>
                                <input asp-for="ThirdName" class="form-control" />
                                <span asp-validation-for="ThirdName" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label asp-for="LastName" class="form-label"></label>
                                <input asp-for="LastName" class="form-control" />
                                <span asp-validation-for="LastName" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label asp-for="Title" class="form-label"></label>
                                <input asp-for="Title" class="form-control" />
                                <span asp-validation-for="Title" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- معلومات الولادة والسكن -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-map-marker-alt me-2"></i>
                                    معلومات الولادة والسكن
                                </h5>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label asp-for="PlaceOfBirth" class="form-label"></label>
                                <input asp-for="PlaceOfBirth" class="form-control" />
                                <span asp-validation-for="PlaceOfBirth" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label asp-for="DateOfBirth" class="form-label"></label>
                                <input asp-for="DateOfBirth" class="form-control" type="date" />
                                <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label asp-for="ResidenceProvince" class="form-label"></label>
                                <select asp-for="ResidenceProvince" class="form-select">
                                    <option value="">اختر المحافظة</option>
                                    <option value="بغداد">بغداد</option>
                                    <option value="البصرة">البصرة</option>
                                    <option value="نينوى">نينوى</option>
                                    <option value="أربيل">أربيل</option>
                                    <option value="النجف">النجف</option>
                                    <option value="كربلاء">كربلاء</option>
                                    <option value="بابل">بابل</option>
                                    <option value="الأنبار">الأنبار</option>
                                    <option value="ديالى">ديالى</option>
                                    <option value="ذي قار">ذي قار</option>
                                    <option value="المثنى">المثنى</option>
                                    <option value="القادسية">القادسية</option>
                                    <option value="ميسان">ميسان</option>
                                    <option value="واسط">واسط</option>
                                    <option value="صلاح الدين">صلاح الدين</option>
                                    <option value="كركوك">كركوك</option>
                                    <option value="دهوك">دهوك</option>
                                    <option value="السليمانية">السليمانية</option>
                                </select>
                                <span asp-validation-for="ResidenceProvince" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label asp-for="MaritalStatus" class="form-label"></label>
                                <select asp-for="MaritalStatus" class="form-select" asp-items="Html.GetEnumSelectList<OfficersManagement.Models.MaritalStatus>()">
                                    <option value="">اختر الحالة الاجتماعية</option>
                                </select>
                                <span asp-validation-for="MaritalStatus" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>
                                        العودة للقائمة
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
}
