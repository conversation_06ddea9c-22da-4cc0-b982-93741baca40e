using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using OfficersManagement.Data;
using OfficersManagement.Models;

namespace OfficersManagement.Controllers
{
    public class OfficersController : Controller
    {
        private readonly ApplicationDbContext _context;

        public OfficersController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Officers
        public async Task<IActionResult> Index(string searchString, string sortOrder)
        {
            ViewData["NameSortParm"] = String.IsNullOrEmpty(sortOrder) ? "name_desc" : "";
            ViewData["RankSortParm"] = sortOrder == "Rank" ? "rank_desc" : "Rank";
            ViewData["DateSortParm"] = sortOrder == "Date" ? "date_desc" : "Date";
            ViewData["CurrentFilter"] = searchString;

            var officers = from o in _context.Officers
                          select o;

            if (!String.IsNullOrEmpty(searchString))
            {
                officers = officers.Where(o => o.FirstName.Contains(searchString)
                                       || o.LastName.Contains(searchString)
                                       || o.EmployeeNumber.Contains(searchString)
                                       || o.Rank.Contains(searchString));
            }

            switch (sortOrder)
            {
                case "name_desc":
                    officers = officers.OrderByDescending(o => o.FirstName);
                    break;
                case "Rank":
                    officers = officers.OrderBy(o => o.Rank);
                    break;
                case "rank_desc":
                    officers = officers.OrderByDescending(o => o.Rank);
                    break;
                case "Date":
                    officers = officers.OrderBy(o => o.DateOfBirth);
                    break;
                case "date_desc":
                    officers = officers.OrderByDescending(o => o.DateOfBirth);
                    break;
                default:
                    officers = officers.OrderBy(o => o.FirstName);
                    break;
            }

            return View(await officers.ToListAsync());
        }

        // GET: Officers/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var officer = await _context.Officers
                .FirstOrDefaultAsync(m => m.Id == id);
            if (officer == null)
            {
                return NotFound();
            }

            return View(officer);
        }

        // GET: Officers/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: Officers/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("EmployeeNumber,StatisticalNumber,Rank,FirstName,SecondName,ThirdName,LastName,Title,PlaceOfBirth,DateOfBirth,ResidenceProvince,MaritalStatus")] Officer officer)
        {
            if (ModelState.IsValid)
            {
                officer.CreatedAt = DateTime.Now;
                officer.UpdatedAt = DateTime.Now;
                _context.Add(officer);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم إضافة الضابط بنجاح";
                return RedirectToAction(nameof(Index));
            }
            return View(officer);
        }

        // GET: Officers/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var officer = await _context.Officers.FindAsync(id);
            if (officer == null)
            {
                return NotFound();
            }
            return View(officer);
        }

        // POST: Officers/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,EmployeeNumber,StatisticalNumber,Rank,FirstName,SecondName,ThirdName,LastName,Title,PlaceOfBirth,DateOfBirth,ResidenceProvince,MaritalStatus,CreatedAt")] Officer officer)
        {
            if (id != officer.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    officer.UpdatedAt = DateTime.Now;
                    _context.Update(officer);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم تحديث بيانات الضابط بنجاح";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!OfficerExists(officer.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(officer);
        }

        // GET: Officers/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var officer = await _context.Officers
                .FirstOrDefaultAsync(m => m.Id == id);
            if (officer == null)
            {
                return NotFound();
            }

            return View(officer);
        }

        // POST: Officers/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var officer = await _context.Officers.FindAsync(id);
            if (officer != null)
            {
                _context.Officers.Remove(officer);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم حذف الضابط بنجاح";
            }

            return RedirectToAction(nameof(Index));
        }

        private bool OfficerExists(int id)
        {
            return _context.Officers.Any(e => e.Id == id);
        }
    }
}
