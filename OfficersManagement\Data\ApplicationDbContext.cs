using Microsoft.EntityFrameworkCore;
using OfficersManagement.Models;

namespace OfficersManagement.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<Officer> Officers { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تكوين جدول الضباط
            modelBuilder.Entity<Officer>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.Property(e => e.EmployeeNumber)
                    .IsRequired()
                    .HasMaxLength(20);

                entity.Property(e => e.StatisticalNumber)
                    .IsRequired()
                    .HasMaxLength(20);

                entity.Property(e => e.Rank)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.FirstName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.SecondName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.ThirdName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.LastName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Title)
                    .HasMaxLength(50);

                entity.Property(e => e.PlaceOfBirth)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.ResidenceProvince)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.DateOfBirth)
                    .IsRequired();

                entity.Property(e => e.MaritalStatus)
                    .IsRequired();

                entity.Property(e => e.CreatedAt)
                    .IsRequired();

                entity.Property(e => e.UpdatedAt)
                    .IsRequired();

                // إنشاء فهارس للبحث السريع
                entity.HasIndex(e => e.EmployeeNumber)
                    .IsUnique()
                    .HasDatabaseName("IX_Officers_EmployeeNumber");

                entity.HasIndex(e => e.StatisticalNumber)
                    .IsUnique()
                    .HasDatabaseName("IX_Officers_StatisticalNumber");

                entity.HasIndex(e => new { e.FirstName, e.LastName })
                    .HasDatabaseName("IX_Officers_Name");
            });

            // إضافة بيانات تجريبية
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            var seedDate = new DateTime(2024, 1, 1, 12, 0, 0);

            modelBuilder.Entity<Officer>().HasData(
                new Officer
                {
                    Id = 1,
                    EmployeeNumber = "EMP001",
                    StatisticalNumber = "STAT001",
                    Rank = "عقيد",
                    FirstName = "أحمد",
                    SecondName = "محمد",
                    ThirdName = "علي",
                    LastName = "الحسن",
                    Title = "",
                    PlaceOfBirth = "بغداد",
                    DateOfBirth = new DateTime(1980, 5, 15),
                    ResidenceProvince = "بغداد",
                    MaritalStatus = MaritalStatus.Married,
                    CreatedAt = seedDate,
                    UpdatedAt = seedDate
                },
                new Officer
                {
                    Id = 2,
                    EmployeeNumber = "EMP002",
                    StatisticalNumber = "STAT002",
                    Rank = "رائد",
                    FirstName = "فاطمة",
                    SecondName = "حسن",
                    ThirdName = "عبدالله",
                    LastName = "الزهراء",
                    Title = "",
                    PlaceOfBirth = "البصرة",
                    DateOfBirth = new DateTime(1985, 8, 22),
                    ResidenceProvince = "البصرة",
                    MaritalStatus = MaritalStatus.Single,
                    CreatedAt = seedDate,
                    UpdatedAt = seedDate
                },
                new Officer
                {
                    Id = 3,
                    EmployeeNumber = "EMP003",
                    StatisticalNumber = "STAT003",
                    Rank = "نقيب",
                    FirstName = "خالد",
                    SecondName = "عبدالرحمن",
                    ThirdName = "صالح",
                    LastName = "المحمود",
                    Title = "",
                    PlaceOfBirth = "الموصل",
                    DateOfBirth = new DateTime(1990, 12, 10),
                    ResidenceProvince = "نينوى",
                    MaritalStatus = MaritalStatus.Married,
                    CreatedAt = seedDate,
                    UpdatedAt = seedDate
                }
            );
        }
    }
}
