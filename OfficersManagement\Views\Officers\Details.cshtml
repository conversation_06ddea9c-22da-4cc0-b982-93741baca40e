@model OfficersManagement.Models.Officer

@{
    ViewData["Title"] = "تفاصيل الضابط";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i>
                        تفاصيل الضابط
                    </h3>
                </div>
                <div class="card-body">
                    <!-- معلومات الهوية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-id-card me-2"></i>
                                معلومات الهوية
                            </h5>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">رقم الموظف:</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-secondary fs-6">@Html.DisplayFor(model => model.EmployeeNumber)</span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">الرقم الإحصائي:</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-secondary fs-6">@Html.DisplayFor(model => model.StatisticalNumber)</span>
                            </p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label fw-bold">الرتبة:</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-info fs-6">@Html.DisplayFor(model => model.Rank)</span>
                            </p>
                        </div>
                    </div>

                    <!-- المعلومات الشخصية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-user me-2"></i>
                                المعلومات الشخصية
                            </h5>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label fw-bold">الاسم الكامل:</label>
                            <p class="form-control-plaintext fs-5 fw-bold text-dark">
                                @Html.DisplayFor(model => model.FullName)
                            </p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">الاسم الأول:</label>
                            <p class="form-control-plaintext">@Html.DisplayFor(model => model.FirstName)</p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">الاسم الثاني:</label>
                            <p class="form-control-plaintext">@Html.DisplayFor(model => model.SecondName)</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">الاسم الثالث:</label>
                            <p class="form-control-plaintext">@Html.DisplayFor(model => model.ThirdName)</p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">اسم العائلة:</label>
                            <p class="form-control-plaintext">@Html.DisplayFor(model => model.LastName)</p>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Title))
                    {
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label fw-bold">اللقب:</label>
                                <p class="form-control-plaintext">@Html.DisplayFor(model => model.Title)</p>
                            </div>
                        </div>
                    }

                    <!-- معلومات الولادة والسكن -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                معلومات الولادة والسكن
                            </h5>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">محل الولادة:</label>
                            <p class="form-control-plaintext">@Html.DisplayFor(model => model.PlaceOfBirth)</p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">تاريخ الولادة:</label>
                            <p class="form-control-plaintext">@Html.DisplayFor(model => model.DateOfBirth)</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">العمر:</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-warning text-dark fs-6">@Html.DisplayFor(model => model.Age)</span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">محافظة السكن:</label>
                            <p class="form-control-plaintext">@Html.DisplayFor(model => model.ResidenceProvince)</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">الحالة الاجتماعية:</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-success fs-6">@Html.DisplayFor(model => model.MaritalStatus)</span>
                            </p>
                        </div>
                    </div>

                    <!-- معلومات النظام -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-clock me-2"></i>
                                معلومات النظام
                            </h5>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">تاريخ الإنشاء:</label>
                            <p class="form-control-plaintext">@Html.DisplayFor(model => model.CreatedAt)</p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">تاريخ آخر تحديث:</label>
                            <p class="form-control-plaintext">@Html.DisplayFor(model => model.UpdatedAt)</p>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a asp-action="Index" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    العودة للقائمة
                                </a>
                                <div>
                                    <a asp-action="Edit" asp-route-id="@Model?.Id" class="btn btn-warning me-2">
                                        <i class="fas fa-edit me-2"></i>
                                        تعديل
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@Model?.Id" class="btn btn-danger">
                                        <i class="fas fa-trash me-2"></i>
                                        حذف
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
}
