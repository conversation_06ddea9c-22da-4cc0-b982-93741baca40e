﻿@{
    ViewData["Title"] = "الصفحة الرئيسية";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Hero Section -->
            <div class="jumbotron bg-primary text-white text-center py-5 mb-4 rounded">
                <div class="container">
                    <h1 class="display-4 fw-bold">
                        <i class="fas fa-shield-alt me-3"></i>
                        نظام إدارة الضباط والمنتسبين
                    </h1>
                    <p class="lead">نظام شامل لإدارة بيانات الضباط والمنتسبين بكفاءة وأمان</p>
                    <hr class="my-4 bg-white">
                    <p class="mb-4">يمكنك من خلال هذا النظام إدارة جميع بيانات الضباط والمنتسبين بسهولة ويسر</p>
                    <a class="btn btn-light btn-lg" asp-controller="Officers" asp-action="Index" role="button">
                        <i class="fas fa-users me-2"></i>
                        عرض قائمة الضباط
                    </a>
                </div>
            </div>

            <!-- Features Section -->
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-users fa-3x text-primary"></i>
                            </div>
                            <h5 class="card-title">إدارة الضباط</h5>
                            <p class="card-text">عرض وإدارة جميع بيانات الضباط والمنتسبين مع إمكانية البحث والتصفية</p>
                            <a href="@Url.Action("Index", "Officers")" class="btn btn-primary">
                                <i class="fas fa-eye me-1"></i>
                                عرض القائمة
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-user-plus fa-3x text-success"></i>
                            </div>
                            <h5 class="card-title">إضافة ضابط جديد</h5>
                            <p class="card-text">إضافة بيانات ضابط أو منتسب جديد مع جميع المعلومات المطلوبة</p>
                            <a href="@Url.Action("Create", "Officers")" class="btn btn-success">
                                <i class="fas fa-plus me-1"></i>
                                إضافة ضابط
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-search fa-3x text-info"></i>
                            </div>
                            <h5 class="card-title">البحث والتصفية</h5>
                            <p class="card-text">البحث في بيانات الضباط بالاسم أو الرتبة أو رقم الموظف</p>
                            <a href="@Url.Action("Index", "Officers")" class="btn btn-info">
                                <i class="fas fa-search me-1"></i>
                                البحث
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Section -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-bar me-2"></i>
                                إحصائيات سريعة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="border-end">
                                        <h3 class="text-primary">
                                            <i class="fas fa-users"></i>
                                        </h3>
                                        <p class="mb-0">إجمالي الضباط</p>
                                        <small class="text-muted">يمكن عرض العدد الإجمالي هنا</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="border-end">
                                        <h3 class="text-success">
                                            <i class="fas fa-star"></i>
                                        </h3>
                                        <p class="mb-0">الضباط</p>
                                        <small class="text-muted">حسب الرتبة</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="border-end">
                                        <h3 class="text-warning">
                                            <i class="fas fa-user-tie"></i>
                                        </h3>
                                        <p class="mb-0">المنتسبين</p>
                                        <small class="text-muted">حسب التصنيف</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div>
                                        <h3 class="text-info">
                                            <i class="fas fa-clock"></i>
                                        </h3>
                                        <p class="mb-0">آخر تحديث</p>
                                        <small class="text-muted">@DateTime.Now.ToString("yyyy/MM/dd")</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
