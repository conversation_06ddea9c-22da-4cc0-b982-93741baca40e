using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OfficersManagement.Models
{
    public class Officer
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "رقم الموظف مطلوب")]
        [Display(Name = "رقم الموظف")]
        [StringLength(20)]
        public string EmployeeNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "الرقم الإحصائي مطلوب")]
        [Display(Name = "الرقم الإحصائي")]
        [StringLength(20)]
        public string StatisticalNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "الرتبة مطلوبة")]
        [Display(Name = "الرتبة")]
        [StringLength(50)]
        public string Rank { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الأول مطلوب")]
        [Display(Name = "الاسم الأول")]
        [StringLength(50)]
        public string FirstName { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الثاني مطلوب")]
        [Display(Name = "الاسم الثاني")]
        [StringLength(50)]
        public string SecondName { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الثالث مطلوب")]
        [Display(Name = "الاسم الثالث")]
        [StringLength(50)]
        public string ThirdName { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم العائلة مطلوب")]
        [Display(Name = "اسم العائلة")]
        [StringLength(50)]
        public string LastName { get; set; } = string.Empty;

        [Display(Name = "اللقب")]
        [StringLength(50)]
        public string? Title { get; set; }

        [Required(ErrorMessage = "محل الولادة مطلوب")]
        [Display(Name = "محل الولادة")]
        [StringLength(100)]
        public string PlaceOfBirth { get; set; } = string.Empty;

        [Required(ErrorMessage = "تاريخ الولادة مطلوب")]
        [Display(Name = "تاريخ الولادة")]
        [DataType(DataType.Date)]
        public DateTime DateOfBirth { get; set; }

        [Required(ErrorMessage = "محافظة السكن مطلوبة")]
        [Display(Name = "محافظة السكن")]
        [StringLength(50)]
        public string ResidenceProvince { get; set; } = string.Empty;

        [Required(ErrorMessage = "الحالة الاجتماعية مطلوبة")]
        [Display(Name = "الحالة الاجتماعية")]
        public MaritalStatus MaritalStatus { get; set; }

        // خاصية محسوبة للعمر
        [Display(Name = "العمر")]
        [NotMapped]
        public string Age
        {
            get
            {
                var today = DateTime.Today;
                var age = today.Year - DateOfBirth.Year;
                
                if (DateOfBirth.Date > today.AddYears(-age))
                    age--;

                var months = today.Month - DateOfBirth.Month;
                if (months < 0)
                {
                    months += 12;
                    age--;
                }

                var days = today.Day - DateOfBirth.Day;
                if (days < 0)
                {
                    months--;
                    if (months < 0)
                    {
                        months += 12;
                        age--;
                    }
                    days += DateTime.DaysInMonth(today.AddMonths(-1).Year, today.AddMonths(-1).Month);
                }

                return $"{age} سنة، {months} شهر، {days} يوم";
            }
        }

        // خاصية محسوبة للاسم الكامل
        [Display(Name = "الاسم الكامل")]
        [NotMapped]
        public string FullName
        {
            get
            {
                var fullName = $"{FirstName} {SecondName} {ThirdName} {LastName}";
                if (!string.IsNullOrEmpty(Title))
                    fullName += $" {Title}";
                return fullName;
            }
        }

        // تواريخ النظام
        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "تاريخ التحديث")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }

    public enum MaritalStatus
    {
        [Display(Name = "أعزب")]
        Single = 1,
        
        [Display(Name = "متزوج")]
        Married = 2,
        
        [Display(Name = "مطلق")]
        Divorced = 3,
        
        [Display(Name = "أرمل")]
        Widowed = 4
    }
}
